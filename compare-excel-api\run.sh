#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}💡 $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_header() {
    echo -e "${CYAN}========================================${NC}"
    echo -e "${CYAN}   Excel API Data Comparison Tool${NC}"
    echo -e "${CYAN}========================================${NC}"
    echo
}

# Main script
clear
print_header

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed!"
    print_info "Please install Node.js:"
    echo "  - macOS: brew install node"
    echo "  - Ubuntu/Debian: sudo apt install nodejs npm"
    echo "  - Or download from https://nodejs.org"
    echo
    exit 1
fi

print_success "Node.js detected: $(node --version)"

# Check if npm is available
if ! command -v npm &> /dev/null; then
    print_error "npm is not available!"
    exit 1
fi

print_success "npm detected: $(npm --version)"
echo

# Check if package.json exists
if [ ! -f "package.json" ]; then
    print_error "package.json not found!"
    print_info "Make sure you're in the correct directory."
    echo
    exit 1
fi

# Check if data.xlsx exists
if [ ! -f "data.xlsx" ]; then
    print_error "data.xlsx not found!"
    print_info "Please make sure the Excel file exists in the current directory."
    echo
    exit 1
fi

print_success "Excel file found: data.xlsx"

# Check file permissions
if [ ! -r "data.xlsx" ]; then
    print_warning "Excel file is not readable, fixing permissions..."
    chmod 644 data.xlsx
fi

# Check if node_modules exists, if not install dependencies
if [ ! -d "node_modules" ]; then
    echo
    echo -e "${PURPLE}📦 Installing dependencies...${NC}"
    npm install
    if [ $? -ne 0 ]; then
        print_error "Failed to install dependencies!"
        exit 1
    fi
    print_success "Dependencies installed successfully!"
else
    print_success "Dependencies already installed"
fi

echo
echo -e "${PURPLE}🚀 Starting Excel API Data Comparison...${NC}"
echo
print_info "This may take a few minutes depending on your data size..."
print_info "You can press Ctrl+C to stop the process anytime"
echo

# Create a timestamp for this run
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
echo "Started at: $TIMESTAMP"
echo

# Run the main script
node compareExcelWithApi.js

# Check if the script ran successfully
if [ $? -eq 0 ]; then
    echo
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}✅ Comparison completed successfully!${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo
    print_success "Results saved to: comparison_results_firstTime.json"
    echo
    
    # Check if result file exists and show file info
    if [ -f "comparison_results_firstTime.json" ]; then
        FILE_SIZE=$(ls -lh comparison_results_firstTime.json | awk '{print $5}')
        echo -e "${CYAN}📊 Result file size: $FILE_SIZE${NC}"
        
        # Show file modification time
        FILE_TIME=$(ls -l comparison_results_firstTime.json | awk '{print $6, $7, $8}')
        echo -e "${CYAN}📅 Created: $FILE_TIME${NC}"
        echo
        
        print_info "You can now open the JSON file to view detailed results"
        print_info "Or use: cat comparison_results_firstTime.json | jq . (if jq is installed)"
    fi
else
    echo
    echo -e "${RED}========================================${NC}"
    echo -e "${RED}❌ An error occurred during comparison!${NC}"
    echo -e "${RED}========================================${NC}"
    echo
    print_info "Please check the error messages above"
    print_info "Common solutions:"
    echo "   - Check your internet connection"
    echo "   - Verify Excel file format"
    echo "   - Make sure all required columns exist"
    echo "   - Check file permissions: chmod 644 data.xlsx"
    echo
fi

# Show completion time
END_TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
echo "Completed at: $END_TIMESTAMP"
echo

# Option to view results immediately
if [ -f "comparison_results_firstTime.json" ]; then
    echo -e "${YELLOW}Would you like to view a summary of results? (y/n)${NC}"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo
        echo -e "${CYAN}📊 Results Summary:${NC}"
        echo "=================="
        
        # Extract summary using grep and basic text processing
        if command -v jq &> /dev/null; then
            # If jq is available, use it for better JSON parsing
            echo "Total rows: $(jq '.metadata.totalRows' comparison_results_firstTime.json)"
            echo "Near correct: $(jq '.metadata.summary.nearCorrect' comparison_results_firstTime.json)"
            echo "Incorrect: $(jq '.metadata.summary.incorrect' comparison_results_firstTime.json)"
            echo "Errors: $(jq '.metadata.summary.errors' comparison_results_firstTime.json)"
        else
            # Basic text processing if jq is not available
            echo "Use 'cat comparison_results_firstTime.json' to view full results"
            echo "Or install jq for better JSON viewing: brew install jq (macOS) or sudo apt install jq (Linux)"
        fi
        echo
    fi
fi

echo -e "${CYAN}Press Enter to exit...${NC}"
read -r
